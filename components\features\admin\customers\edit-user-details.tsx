import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { editUserDetailsSchema, EditUserDetails as EditUserDetailsType } from "@/pages/api/users/[slug]/user-details";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { useUpdateUserDetailsMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export interface EditUserDetailsProps {
    user: PublicUserWithCustomer;
    onCancel: () => void;
    onSuccess: () => void;
}

// Custom phone validation schema (copied from signup page)
const phoneValidation = z
    .string()
    .regex(/^[0-9+\-\(\)\s]*$/, {
        message: "Phone number can only contain numbers, +, -, (, ), and spaces",
    })
    .optional()
    .or(z.literal(""));

export function EditUserDetails({ user, onCancel, onSuccess }: EditUserDetailsProps) {
    const { toast } = useToast();
    const token = useAuthStore((state) => state.token);
    const customerData = user.customer_data?.[0];
    const businessDetails = user.business_details;

    console.log(businessDetails.website)

    // Initialize form with current user data
    const form = useForm<EditUserDetailsType>({
        resolver: zodResolver(editUserDetailsSchema),
        defaultValues: {
            first_name: user.first_name || "",
            last_name: user.last_name || "",
            notes: user.notes || "",
            website: businessDetails?.website || "",
            company_name: customerData?.company_name || "",
            phone: user?.phone || "",
            primary_contact_name: customerData?.primary_contact_name || "",
            shipping_notes: customerData?.shipping_notes || "",
            buyer_name: businessDetails?.buyer_name || "",
            business_type: businessDetails?.business_type || "",
            account_payable_contact: businessDetails?.account_payable_contact || "",
            account_payable_phone: businessDetails?.account_payable_phone || "",
            authorized_contact_name: businessDetails?.authorized_contact_name || "",
            business_nature: businessDetails?.business_nature || "",
            has_mechanic: businessDetails?.has_mechanic || "",
            maxton_account: businessDetails?.maxton_account || "",
            number_of_elevators: businessDetails?.number_of_elevators || undefined,
            technical_contact: businessDetails?.technical_contact || "",
            technical_contact_phone: businessDetails?.technical_contact_phone || "",
            technician: businessDetails?.technician || "",
        },
    });

    const updateUserDetailsMutation = useUpdateUserDetailsMutation(user.id, token || "");

    const onSubmit = async (data: EditUserDetailsType) => {
        try {
            // Convert number_of_elevators to number if it's a string
            const processedData = {
                ...data,
                number_of_elevators: data.number_of_elevators
                    ? Number(data.number_of_elevators)
                    : undefined,
            };

            await updateUserDetailsMutation.mutateAsync(processedData);
            toast({
                title: "Success",
                description: "User details updated successfully",
            });
            onSuccess();
        } catch (error) {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to update user details",
                variant: "destructive",
            });
        }
    };

    const isLoading = updateUserDetailsMutation.isPending;

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Personal Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Personal Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="first_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>First Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="First name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="last_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Last Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Last name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="maxton_account"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Customer Account#</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Maxton account" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Email</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                            <div className="text-sm font-medium">Role</div>
                            <div className="text-sm text-gray-500">{user.role}</div>
                        </div>

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Created At</div>
                            <div className="text-sm text-gray-500">
                                {new Date(user.created_at).toLocaleDateString()}
                            </div>
                        </div>

                        <div className="col-span-2">
                            <FormField
                                control={form.control}
                                name="notes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Notes</FormLabel>
                                        <FormControl>
                                            <Textarea {...field} placeholder="Notes" disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
                </div>

                {/* Business Details */}
                <div className="space-y-4 mt-4 border-t pt-4">
                    <h3 className="text-lg font-semibold">Business Details</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="website"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Website</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="https://example.com" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="buyer_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Buyer Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Buyer name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="business_type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Business Type</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Business type" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="business_nature"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Business Nature</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Business nature" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="has_mechanic"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Has Mechanic</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="yes/no/use service company" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="number_of_elevators"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Number Of Elevators</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            type="number"
                                            placeholder="0"
                                            disabled={isLoading}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="authorized_contact_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Authorized Contact Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Authorized contact name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="technical_contact"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Technical Contact</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Technical contact" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="technical_contact_phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Technical Contact Phone</FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            {...field}
                                            placeholder="Enter technical contact phone"
                                            defaultCountry="US"
                                            disabled={isLoading}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="account_payable_contact"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Account Payable Contact</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Account payable contact" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="account_payable_phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Account Payable Phone</FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            {...field}
                                            placeholder="Enter account payable phone"
                                            defaultCountry="US"
                                            disabled={isLoading}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="technician"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Technician</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Technician" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {/* Customer Information */}
                <div className="space-y-4 mt-4 border-t pt-4">
                    <h3 className="text-lg font-semibold">Customer Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="company_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Company Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Company name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="primary_contact_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Contact Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} placeholder="Primary contact name" disabled={isLoading} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Phone</FormLabel>
                                    <FormControl>
                                        <PhoneInput
                                            {...field}
                                            placeholder="Enter phone number"
                                            defaultCountry="US"
                                            disabled={isLoading}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Customer Number</div>
                            <div className="text-sm text-gray-500">
                                {customerData?.customer_number || "N/A"}
                            </div>
                        </div>

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Credit Limit</div>
                            <div className="text-sm text-gray-500">
                                {customerData?.credit_limit !== null
                                    ? `$${customerData?.credit_limit?.toFixed(2)}`
                                    : "N/A"}
                            </div>
                        </div>

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Customer Role</div>
                            <div className="text-sm text-gray-500">
                                {customerData?.role || "N/A"}
                            </div>
                        </div>

                        <div className="grid grid-rows-2 gap-2">
                            <div className="text-sm font-medium">Group</div>
                            <div className="text-sm text-gray-500">
                                {customerData?.group_data?.data?.name || "None"}
                            </div>
                        </div>

                        <div className="col-span-2">
                            <FormField
                                control={form.control}
                                name="shipping_notes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Shipping Notes</FormLabel>
                                        <FormControl>
                                            <Textarea {...field} placeholder="Shipping notes" disabled={isLoading} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-2 pt-4 border-t">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                        disabled={isLoading}
                    >
                        Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Save Changes
                    </Button>
                </div>
            </form>
        </Form>
    );
}